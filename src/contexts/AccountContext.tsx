'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Account } from '@/types';

interface AccountContextType {
  accounts: Account[];
  activeAccount: Account | null;
  addAccount: (account: Omit<Account, 'id' | 'createdAt'>) => void;
  updateAccount: (id: string, updates: Partial<Account>) => void;
  deleteAccount: (id: string) => void;
  setActiveAccount: (account: Account | null) => void;
  getAccountById: (id: string) => Account | undefined;
}

const AccountContext = createContext<AccountContextType | undefined>(undefined);

export function AccountProvider({ children }: { children: ReactNode }) {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [activeAccount, setActiveAccount] = useState<Account | null>(null);

  // 从localStorage加载账户数据
  useEffect(() => {
    const savedAccounts = localStorage.getItem('bam-accounts');
    const savedActiveAccountId = localStorage.getItem('bam-active-account');
    
    if (savedAccounts) {
      try {
        const parsedAccounts = JSON.parse(savedAccounts).map((acc: Account & { createdAt: string }) => ({
          ...acc,
          createdAt: new Date(acc.createdAt)
        }));
        setAccounts(parsedAccounts);
        
        if (savedActiveAccountId) {
          const activeAcc = parsedAccounts.find((acc: Account) => acc.id === savedActiveAccountId);
          if (activeAcc) {
            setActiveAccount(activeAcc);
          }
        }
      } catch (error) {
        console.error('Failed to load accounts from localStorage:', error);
      }
    }
  }, []);

  // 保存账户数据到localStorage
  useEffect(() => {
    localStorage.setItem('bam-accounts', JSON.stringify(accounts));
  }, [accounts]);

  // 保存活跃账户ID到localStorage
  useEffect(() => {
    if (activeAccount) {
      localStorage.setItem('bam-active-account', activeAccount.id);
    } else {
      localStorage.removeItem('bam-active-account');
    }
  }, [activeAccount]);

  const addAccount = (accountData: Omit<Account, 'id' | 'createdAt'>) => {
    const newAccount: Account = {
      ...accountData,
      id: Date.now().toString(),
      createdAt: new Date(),
    };
    
    setAccounts(prev => [...prev, newAccount]);
    
    // 如果这是第一个账户，自动设为活跃账户
    if (accounts.length === 0) {
      setActiveAccount(newAccount);
    }
  };

  const updateAccount = (id: string, updates: Partial<Account>) => {
    setAccounts(prev => 
      prev.map(account => 
        account.id === id ? { ...account, ...updates } : account
      )
    );
    
    // 如果更新的是活跃账户，也要更新activeAccount
    if (activeAccount?.id === id) {
      setActiveAccount(prev => prev ? { ...prev, ...updates } : null);
    }
  };

  const deleteAccount = (id: string) => {
    setAccounts(prev => prev.filter(account => account.id !== id));
    
    // 如果删除的是活跃账户，清除活跃账户
    if (activeAccount?.id === id) {
      setActiveAccount(null);
    }
  };

  const getAccountById = (id: string) => {
    return accounts.find(account => account.id === id);
  };

  const value: AccountContextType = {
    accounts,
    activeAccount,
    addAccount,
    updateAccount,
    deleteAccount,
    setActiveAccount,
    getAccountById,
  };

  return (
    <AccountContext.Provider value={value}>
      {children}
    </AccountContext.Provider>
  );
}

export function useAccounts() {
  const context = useContext(AccountContext);
  if (context === undefined) {
    throw new Error('useAccounts must be used within an AccountProvider');
  }
  return context;
}