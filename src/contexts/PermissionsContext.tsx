'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAccounts } from './AccountContext';
import { ApiPermissions } from '@/types';
import { BinanceService } from '@/lib/binance';

interface PermissionsContextType {
  permissions: ApiPermissions | null;
  loading: boolean;
  error: string | null;
  canAccessSpot: boolean;
  canAccessFutures: boolean;
  canTrade: boolean;
  canTradeSpot: boolean;
  canTradeFutures: boolean;
  canWithdraw: boolean;
  refreshPermissions: () => Promise<void>;
}

const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

export function PermissionsProvider({ children }: { children: ReactNode }) {
  const { activeAccount } = useAccounts();
  const [permissions, setPermissions] = useState<ApiPermissions | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPermissions = async () => {
    if (!activeAccount) {
      setPermissions(null);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const binanceService = new BinanceService(activeAccount);
      const result = await binanceService.getApiPermissions();

      if (result.success && result.data) {
        setPermissions(result.data);
      } else {
        setError(result.error || '获取API权限信息失败');
        setPermissions(null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取API权限信息失败');
      setPermissions(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, [activeAccount]);

  // 计算权限状态
  const canAccessSpot = permissions?.基本信息?.['读取权限'] === '已启用' || false;
  const canAccessFutures = permissions?.基本信息?.['读取权限'] === '已启用' || false;
  const canTradeSpot = permissions?.交易权限?.['现货和杠杆交易'] === '已启用' || false;
  const canTradeFutures = permissions?.交易权限?.['期货交易'] === '已启用' || false;
  const canTrade = canTradeSpot || canTradeFutures;
  const canWithdraw = permissions?.转账权限?.['提现权限'] === '已启用' || false;

  const value: PermissionsContextType = {
    permissions,
    loading,
    error,
    canAccessSpot,
    canAccessFutures,
    canTrade,
    canTradeSpot,
    canTradeFutures,
    canWithdraw,
    refreshPermissions: fetchPermissions,
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
}

export function usePermissions() {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
}