// 账户类型定义
export interface Account {
  id: string;
  name: string;
  apiKey: string;
  apiSecret: string;
  isTestnet: boolean;
  isActive: boolean;
  createdAt: Date;
}

// 余额信息
export interface Balance {
  asset: string;
  free: string;
  locked: string;
  total: string;
  usdValue?: string;
}

// 持仓信息
export interface Position {
  symbol: string;
  side: 'long' | 'short';
  size: string;
  entryPrice: string;
  markPrice: string;
  pnl: string;
  pnlPercentage: string;
  leverage: string;
  marginType: 'isolated' | 'cross';
}

// 订单类型
export interface Order {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop' | 'stop_market';
  amount: string;
  price?: string;
  stopPrice?: string;
  status: 'open' | 'closed' | 'canceled' | 'partially_filled';
  filled: string;
  remaining: string;
  timestamp: number;
  fee?: string;
}

// 市场数据
export interface Ticker {
  symbol: string;
  last: string;
  bid: string;
  ask: string;
  change: string;
  percentage: string;
  volume: string;
  high: string;
  low: string;
}

// 详细市场数据
export interface MarketData {
  symbol: string;
  price: string;
  bid: string;
  ask: string;
  high24h: string;
  low24h: string;
  change24h: string;
  changePercent24h: string;
  volume24h: string;
  quoteVolume24h: string;
}

// 交易对信息
export interface Market {
  id: string;
  symbol: string;
  base: string;
  quote: string;
  active: boolean;
  type: 'spot' | 'future';
  precision: {
    amount: number;
    price: number;
  };
  limits: {
    amount: {
      min: number;
      max: number;
    };
    price: {
      min: number;
      max: number;
    };
  };
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 交易表单数据
export interface TradeForm {
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit';
  amount: string;
  price?: string;
  leverage?: string;
}

// 账户统计
export interface AccountStats {
  totalBalance: string;
  totalPnl: string;
  totalPnlPercentage: string;
  openPositions: number;
  openOrders: number;
}

// 杠杆信息
export interface LeverageInfo {
  symbol: string;
  leverage: number;
  maxLeverage: number;
  marginType: 'isolated' | 'cross';
}

// API权限信息
export interface ApiPermissions {
  基本信息: {
    IP限制: string;
    创建时间: string;
    读取权限: string;
  };
  交易权限: {
    现货和杠杆交易: string;
    期货交易: string;
    杠杆交易: string;
    期权交易: string;
    组合保证金交易: string;
  };
  转账权限: {
    提现权限: string;
    内部转账: string;
    通用转账: string;
  };
  API权限: {
    FIX_API交易: string;
    FIX_API只读: string;
  };
  原始数据: {
    ipRestrict: boolean;
    createTime: number;
    enableReading: boolean;
    enableWithdrawals: boolean;
    enableInternalTransfer: boolean;
    enableMargin: boolean;
    enableFutures: boolean;
    permitsUniversalTransfer: boolean;
    enableVanillaOptions: boolean;
    enableFixApiTrade: boolean;
    enableFixReadOnly: boolean;
    enableSpotAndMarginTrading: boolean;
    enablePortfolioMarginTrading: boolean;
  };
}