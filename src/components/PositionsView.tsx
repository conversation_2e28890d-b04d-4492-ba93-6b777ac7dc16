'use client';

import React, { useState, useEffect } from 'react';
import { useAccounts } from '@/contexts/AccountContext';
import { Position } from '@/types';
import { BinanceService } from '@/lib/binance';
import { Button } from '@/components/ui/Button';

export function PositionsView() {
  const { activeAccount } = useAccounts();
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchPositions = async () => {
    if (!activeAccount) {
      setError('请先选择一个活跃账户');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const binanceService = new BinanceService(activeAccount);
      const result = await binanceService.getFuturesPositions();

      if (result.success && result.data) {
        setPositions(result.data);
        setLastUpdated(new Date());
      } else {
        setError(result.error || '获取持仓失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取持仓失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeAccount) {
      fetchPositions();
    }
  }, [activeAccount]);

  const formatNumber = (value: string) => {
    const num = parseFloat(value);
    if (num === 0) return '0';
    if (Math.abs(num) < 0.000001) return num.toExponential(2);
    if (Math.abs(num) < 1) return num.toFixed(6);
    if (Math.abs(num) < 1000) return num.toFixed(4);
    return num.toLocaleString();
  };

  const formatPnl = (pnl: string, percentage: string) => {
    const pnlNum = parseFloat(pnl);
    const percentageNum = parseFloat(percentage);
    const isPositive = pnlNum >= 0;
    
    return {
      value: `${isPositive ? '+' : ''}${formatNumber(pnl)}`,
      percentage: `${isPositive ? '+' : ''}${percentageNum.toFixed(2)}%`,
      color: isPositive ? 'text-green-600' : 'text-red-600',
      bgColor: isPositive ? 'bg-green-50' : 'bg-red-50',
    };
  };

  const formatSymbol = (symbol: string) => {
    // 将 PUMP/USDT:USDT 格式转换为 PUMP:USDT 格式
    if (symbol.includes('/') && symbol.includes(':')) {
      const parts = symbol.split('/');
      if (parts.length === 2) {
        const base = parts[0];
        const quotePart = parts[1];
        if (quotePart.includes(':')) {
          const quote = quotePart.split(':')[0];
          return `${base}:${quote}`;
        }
      }
    }
    return symbol;
  };

  const getTotalPnl = () => {
    return positions.reduce((total, position) => {
      return total + parseFloat(position.pnl);
    }, 0);
  };

  if (!activeAccount) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">合约持仓</h2>
        <div className="text-center py-8">
          <p className="text-gray-500">请先选择一个活跃账户</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">合约持仓</h2>
          <p className="text-sm text-gray-500">
            账户: {activeAccount.name}
            {activeAccount.isTestnet && ' (测试网)'}
          </p>
        </div>
        <Button onClick={fetchPositions} loading={loading}>
          刷新
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {lastUpdated && (
        <p className="text-sm text-gray-500 mb-4">
          最后更新: {lastUpdated.toLocaleString()}
        </p>
      )}

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">加载中...</p>
        </div>
      ) : positions.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">没有持仓</p>
        </div>
      ) : (
        <>
          {/* 总盈亏显示 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="text-center">
              <p className="text-sm text-gray-600">总未实现盈亏</p>
              <p className={`text-2xl font-bold ${
                getTotalPnl() >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {getTotalPnl() >= 0 ? '+' : ''}{formatNumber(getTotalPnl().toString())} USDT
              </p>
            </div>
          </div>

          {/* 持仓列表 */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    交易对
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    方向
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    开仓价格
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    标记价格
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    未实现盈亏
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    杠杆
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    保证金模式
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {positions.map((position, index) => {
                  const pnlInfo = formatPnl(position.pnl, position.pnlPercentage);
                  return (
                    <tr key={`${position.symbol}-${position.side}-${position.marginType}-${index}`} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatSymbol(position.symbol)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          position.side === 'long' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {position.side === 'long' ? '做多' : '做空'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatNumber(position.size)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${formatNumber(position.entryPrice)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${formatNumber(position.markPrice)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${pnlInfo.color}`}>
                          {pnlInfo.value} USDT
                        </div>
                        <div className={`text-xs ${pnlInfo.color}`}>
                          {pnlInfo.percentage}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {position.leverage}x
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          position.marginType === 'isolated'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {position.marginType === 'isolated' ? '逐仓' : '全仓'}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
}