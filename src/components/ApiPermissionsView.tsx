'use client';

import React, { useState, useEffect } from 'react';
import { useAccounts } from '@/contexts/AccountContext';
import { ApiPermissions } from '@/types';
import { BinanceService } from '@/lib/binance';
import { Button } from '@/components/ui/Button';

export function ApiPermissionsView() {
  const { activeAccount } = useAccounts();
  const [permissions, setPermissions] = useState<ApiPermissions | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchPermissions = async () => {
    if (!activeAccount) {
      setError('请先选择一个活跃账户');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const binanceService = new BinanceService(activeAccount);
      const result = await binanceService.getApiPermissions();

      if (result.success && result.data) {
        setPermissions(result.data);
        setLastUpdated(new Date());
      } else {
        setError(result.error || '获取API权限信息失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取API权限信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeAccount) {
      fetchPermissions();
    }
  }, [activeAccount]);

  const renderPermissionSection = (title: string, items: Record<string, string>) => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">{title}</h3>
      <div className="space-y-2">
        {Object.entries(items).map(([key, value]) => (
          <div key={key} className="flex justify-between items-center py-1">
            <span className="text-gray-600">{key}:</span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              value === '已启用' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {value}
            </span>
          </div>
        ))}
      </div>
    </div>
  );

  if (!activeAccount) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">请先选择一个活跃账户</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-900">API权限信息</h2>
          <p className="text-gray-600 mt-1">当前账户: {activeAccount.name}</p>
          {lastUpdated && (
            <p className="text-sm text-gray-500 mt-1">
              最后更新: {lastUpdated.toLocaleString('zh-CN')}
            </p>
          )}
        </div>
        <Button 
          onClick={fetchPermissions} 
          loading={loading}
          className="min-w-[100px]"
        >
          刷新
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {loading && (
        <div className="text-center py-8">
          <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            正在获取权限信息...
          </div>
        </div>
      )}

      {permissions && !loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {renderPermissionSection('基本信息', permissions.基本信息)}
          {renderPermissionSection('交易权限', permissions.交易权限)}
          {renderPermissionSection('转账权限', permissions.转账权限)}
          {renderPermissionSection('API权限', permissions.API权限)}
        </div>
      )}

      {permissions && !loading && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">原始数据</h3>
          <pre className="bg-white p-4 rounded border text-sm overflow-x-auto">
            {JSON.stringify(permissions.原始数据, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}