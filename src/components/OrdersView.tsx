'use client';

import React, { useState, useEffect } from 'react';
import { useAccounts } from '@/contexts/AccountContext';
import { Order } from '@/types';
import { BinanceService } from '@/lib/binance';
import { Button } from '@/components/ui/Button';

export function OrdersView() {
  const { activeAccount } = useAccounts();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [cancellingOrders, setCancellingOrders] = useState<Set<string>>(new Set());
  const [showSymbolConfig, setShowSymbolConfig] = useState(false);
  const [configuredSymbols, setConfiguredSymbols] = useState<string[]>([
    'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'XRP/USDT',
    'SOL/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'MATIC/USDT'
  ]);
  const [newSymbol, setNewSymbol] = useState('');

  const fetchOrders = async () => {
    if (!activeAccount) {
      setError('请先选择一个活跃账户');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const binanceService = new BinanceService(activeAccount);
      const result = await binanceService.getOpenOrders(configuredSymbols);

      if (result.success && result.data) {
        setOrders(result.data);
        setLastUpdated(new Date());
      } else {
        setError(result.error || '获取订单失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取订单失败');
    } finally {
      setLoading(false);
    }
  };

  const cancelOrder = async (order: Order) => {
    if (!activeAccount) return;

    setCancellingOrders(prev => new Set(prev).add(order.id));

    try {
      const binanceService = new BinanceService(activeAccount);
      const result = await binanceService.cancelOrder(order.id, order.symbol);

      if (result.success) {
        // 从列表中移除已取消的订单
        setOrders(prev => prev.filter(o => o.id !== order.id));
      } else {
        alert(`取消订单失败: ${result.error}`);
      }
    } catch (err) {
      alert(`取消订单失败: ${err instanceof Error ? err.message : '未知错误'}`);
    } finally {
      setCancellingOrders(prev => {
        const newSet = new Set(prev);
        newSet.delete(order.id);
        return newSet;
      });
    }
  };

  useEffect(() => {
    if (activeAccount) {
      fetchOrders();
    }
  }, [activeAccount]);

  const formatNumber = (value: string) => {
    const num = parseFloat(value);
    if (num === 0) return '0';
    if (Math.abs(num) < 0.000001) return num.toExponential(2);
    if (Math.abs(num) < 1) return num.toFixed(6);
    if (Math.abs(num) < 1000) return num.toFixed(4);
    return num.toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800';
      case 'partially_filled':
        return 'bg-yellow-100 text-yellow-800';
      case 'closed':
        return 'bg-green-100 text-green-800';
      case 'canceled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open':
        return '未成交';
      case 'partially_filled':
        return '部分成交';
      case 'closed':
        return '已成交';
      case 'canceled':
        return '已取消';
      default:
        return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'market':
        return '市价单';
      case 'limit':
        return '限价单';
      case 'stop':
        return '止损单';
      case 'stop_market':
        return '止损市价单';
      default:
        return type;
    }
  };

  if (!activeAccount) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">未成交订单</h2>
        <div className="text-center py-8">
          <p className="text-gray-500">请先选择一个活跃账户</p>
        </div>
      </div>
    );
  }

  const addSymbol = () => {
    if (newSymbol && !configuredSymbols.includes(newSymbol)) {
      setConfiguredSymbols(prev => [...prev, newSymbol]);
      setNewSymbol('');
    }
  };

  const removeSymbol = (symbol: string) => {
    setConfiguredSymbols(prev => prev.filter(s => s !== symbol));
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">未成交订单</h2>
          <p className="text-sm text-gray-500">
            账户: {activeAccount.name}
            {activeAccount.isTestnet && ' (测试网)'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={() => setShowSymbolConfig(!showSymbolConfig)}
            variant="secondary"
          >
            配置交易对
          </Button>
          <Button onClick={fetchOrders} loading={loading}>
            刷新
          </Button>
        </div>
      </div>

      {/* 交易对配置面板 */}
      {showSymbolConfig && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-3">配置查询的交易对</h3>
          <p className="text-sm text-gray-600 mb-4">
            当前配置了 {configuredSymbols.length} 个交易对，系统将查询这些交易对的未成交订单
          </p>
          
          {/* 添加新交易对 */}
          <div className="flex gap-2 mb-4">
            <input
              type="text"
              value={newSymbol}
              onChange={(e) => setNewSymbol(e.target.value.toUpperCase())}
              placeholder="输入交易对，如 BTC/USDT"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && addSymbol()}
            />
            <Button onClick={addSymbol} disabled={!newSymbol || configuredSymbols.includes(newSymbol)}>
              添加
            </Button>
          </div>
          
          {/* 已配置的交易对列表 */}
          <div className="flex flex-wrap gap-2">
            {configuredSymbols.map((symbol) => (
              <div key={symbol} className="flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                <span>{symbol}</span>
                <button
                  onClick={() => removeSymbol(symbol)}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}



      {lastUpdated && (
        <p className="text-sm text-gray-500 mb-4">
          最后更新: {lastUpdated.toLocaleString()}
        </p>
      )}

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">加载中...</p>
        </div>
      ) : orders.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">没有未成交订单</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  交易对
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  方向
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  类型
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  数量
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  价格
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  已成交
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {orders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {order.symbol}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      order.side === 'buy' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {order.side === 'buy' ? '买入' : '卖出'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {getTypeText(order.type)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatNumber(order.amount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {order.price ? `$${formatNumber(order.price)}` : '市价'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatNumber(order.filled)} / {formatNumber(order.amount)}
                    <div className="text-xs text-gray-500">
                      {((parseFloat(order.filled) / parseFloat(order.amount)) * 100).toFixed(1)}%
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                      {getStatusText(order.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(order.timestamp).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {order.status === 'open' || order.status === 'partially_filled' ? (
                      <Button
                        size="sm"
                        variant="danger"
                        onClick={() => cancelOrder(order)}
                        loading={cancellingOrders.has(order.id)}
                      >
                        取消
                      </Button>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}