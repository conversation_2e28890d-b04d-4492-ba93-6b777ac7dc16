'use client';

import React, { useState } from 'react';
import { useAccounts } from '@/contexts/AccountContext';
import { Account } from '@/types';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Modal } from '@/components/ui/Modal';
import { BinanceService } from '@/lib/binance';

interface AccountFormData {
  name: string;
  apiKey: string;
  apiSecret: string;
  isTestnet: boolean;
}

export function AccountManager() {
  const { accounts, activeAccount, addAccount, updateAccount, deleteAccount, setActiveAccount } = useAccounts();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [formData, setFormData] = useState<AccountFormData>({
    name: '',
    apiKey: '',
    apiSecret: '',
    isTestnet: false,
  });
  const [errors, setErrors] = useState<Partial<AccountFormData>>({});
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const resetForm = () => {
    setFormData({
      name: '',
      apiKey: '',
      apiSecret: '',
      isTestnet: false,
    });
    setErrors({});
    setEditingAccount(null);
  };

  const openAddModal = () => {
    resetForm();
    setIsModalOpen(true);
  };

  const openEditModal = (account: Account) => {
    setFormData({
      name: account.name,
      apiKey: account.apiKey,
      apiSecret: account.apiSecret,
      isTestnet: account.isTestnet,
    });
    setEditingAccount(account);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    resetForm();
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<AccountFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = '账户名称不能为空';
    }

    if (!formData.apiKey.trim()) {
      newErrors.apiKey = 'API Key不能为空';
    }

    if (!formData.apiSecret.trim()) {
      newErrors.apiSecret = 'API Secret不能为空';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const testConnection = async () => {
    if (!validateForm()) return;

    setIsTestingConnection(true);
    try {
      const testAccount: Account = {
        id: 'test',
        name: formData.name,
        apiKey: formData.apiKey,
        apiSecret: formData.apiSecret,
        isTestnet: formData.isTestnet,
        isActive: true,
        createdAt: new Date(),
      };

      const binanceService = new BinanceService(testAccount);
      const result = await binanceService.testConnection();

      if (result.success) {
        const message = result.message || '连接测试成功！';
        alert(message);
      } else {
        alert(`连接测试失败: ${result.error}`);
      }
    } catch (error) {
      alert(`连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      if (editingAccount) {
        updateAccount(editingAccount.id, {
          name: formData.name,
          apiKey: formData.apiKey,
          apiSecret: formData.apiSecret,
          isTestnet: formData.isTestnet,
        });
      } else {
        addAccount({
          name: formData.name,
          apiKey: formData.apiKey,
          apiSecret: formData.apiSecret,
          isTestnet: formData.isTestnet,
          isActive: true,
        });
      }
      closeModal();
    } catch (error) {
      alert(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const handleDelete = (account: Account) => {
    if (confirm(`确定要删除账户 "${account.name}" 吗？`)) {
      deleteAccount(account.id);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Button onClick={openAddModal} className="w-full">添加账户</Button>
      </div>

      {accounts.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">还没有添加任何账户</p>
          <Button onClick={openAddModal}>添加第一个账户</Button>
        </div>
      ) : (
        <div className="space-y-4">
          {accounts.map((account) => (
            <div
              key={account.id}
              className={`border rounded-lg p-4 transition-all hover:shadow-md ${
                activeAccount?.id === account.id
                  ? 'border-blue-500 bg-blue-50 shadow-sm'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <h3 className="font-semibold text-gray-900 text-lg">{account.name}</h3>
                  {account.isTestnet && (
                    <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                      测试网
                    </span>
                  )}
                  {activeAccount?.id === account.id && (
                    <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                      当前活跃
                    </span>
                  )}
                </div>
                <div className="flex gap-2">
                  {activeAccount?.id !== account.id && (
                    <button
                      onClick={() => setActiveAccount(account)}
                      className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="设为活跃"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </button>
                  )}
                  <button
                    onClick={() => openEditModal(account)}
                    className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="编辑"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => handleDelete(account)}
                    className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="删除"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={editingAccount ? '编辑账户' : '添加账户'}
        size="md"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="账户名称"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            error={errors.name}
            placeholder="输入账户名称"
          />

          <Input
            label="API Key"
            value={formData.apiKey}
            onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
            error={errors.apiKey}
            placeholder="输入币安API Key"
          />

          <Input
            label="API Secret"
            type="password"
            value={formData.apiSecret}
            onChange={(e) => setFormData({ ...formData, apiSecret: e.target.value })}
            error={errors.apiSecret}
            placeholder="输入币安API Secret"
          />

          <div className="flex items-center">
            <input
              type="checkbox"
              id="isTestnet"
              checked={formData.isTestnet}
              onChange={(e) => setFormData({ ...formData, isTestnet: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isTestnet" className="ml-2 block text-sm text-gray-900">
              使用测试网
            </label>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={testConnection}
              loading={isTestingConnection}
              className="flex-1"
            >
              测试连接
            </Button>
            <Button type="submit" className="flex-1">
              {editingAccount ? '更新' : '添加'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
}