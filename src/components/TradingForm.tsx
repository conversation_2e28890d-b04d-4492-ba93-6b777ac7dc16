'use client';

import React, { useState, useEffect } from 'react';
import { useAccounts } from '@/contexts/AccountContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { Market } from '@/types';
import { BinanceService } from '@/lib/binance';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

interface TradeForm {
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit';
  amount: string;
  price: string;
  leverage: string;
}

export function TradingForm() {
  const { activeAccount } = useAccounts();
  const { canAccessSpot, canAccessFutures, canTrade, canTradeSpot, canTradeFutures } = usePermissions();
  const [markets, setMarkets] = useState<Market[]>([]);
  const [filteredMarkets, setFilteredMarkets] = useState<Market[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMarket, setSelectedMarket] = useState<Market | null>(null);
  const [marketType, setMarketType] = useState<'spot' | 'future'>('spot');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<TradeForm>({
    symbol: '',
    side: 'buy',
    type: 'market',
    amount: '',
    price: '',
    leverage: '1',
  });
  const [currentPrice, setCurrentPrice] = useState<string>('');

  const fetchMarkets = async () => {
    if (!activeAccount) return;

    setLoading(true);
    try {
      const binanceService = new BinanceService(activeAccount);
      const result = await binanceService.getMarkets();

      if (result.success && result.data) {
        setMarkets(result.data);
        filterMarkets(result.data, marketType, searchTerm);
      } else {
        setError(result.error || '获取市场数据失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取市场数据失败');
    } finally {
      setLoading(false);
    }
  };

  const filterMarkets = (allMarkets: Market[], type: 'spot' | 'future', search: string) => {
    const filtered = allMarkets.filter(market => 
      market.active && 
      market.type === type &&
      (market.symbol.toLowerCase().includes(search.toLowerCase()) ||
       market.base.toLowerCase().includes(search.toLowerCase()) ||
       market.quote.toLowerCase().includes(search.toLowerCase()))
    );
    
    // 优先显示USDT交易对
    filtered.sort((a, b) => {
      if (a.quote === 'USDT' && b.quote !== 'USDT') return -1;
      if (a.quote !== 'USDT' && b.quote === 'USDT') return 1;
      return a.symbol.localeCompare(b.symbol);
    });
    
    setFilteredMarkets(filtered.slice(0, 50)); // 限制显示数量
  };

  useEffect(() => {
    if (activeAccount) {
      fetchMarkets();
    }
  }, [activeAccount]);

  useEffect(() => {
    filterMarkets(markets, marketType, searchTerm);
  }, [markets, marketType, searchTerm]);

  const handleMarketTypeChange = (type: 'spot' | 'future') => {
    setMarketType(type);
    setSelectedMarket(null);
    setFormData(prev => ({ ...prev, symbol: '' }));
  };

  const handleMarketSelect = async (market: Market) => {
    setSelectedMarket(market);
    setFormData(prev => ({ ...prev, symbol: market.symbol }));
    
    // 获取当前价格
    if (activeAccount) {
      try {
        const binanceService = new BinanceService(activeAccount);
        const result = await binanceService.getTicker(market.symbol);
        if (result.success && result.data) {
           setCurrentPrice(result.data.price || '');
        }
      } catch (err) {
        console.error('获取价格失败:', err);
      }
    }
  };

  const calculateTotal = () => {
    if (formData.type === 'market') return '市价';
    if (!formData.amount || !formData.price) return '0';
    return (parseFloat(formData.amount) * parseFloat(formData.price)).toFixed(6);
  };

  const validateForm = (): string | null => {
    if (!selectedMarket) return '请选择交易对';
    if (!formData.amount || parseFloat(formData.amount) <= 0) return '请输入有效的数量';
    if (formData.type === 'limit' && (!formData.price || parseFloat(formData.price) <= 0)) {
      return '请输入有效的价格';
    }
    if (marketType === 'future' && (!formData.leverage || parseFloat(formData.leverage) < 1)) {
      return '请输入有效的杠杆倍数';
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!activeAccount || !selectedMarket) return;
    
    // 权限检查
    const hasPermission = marketType === 'spot' ? canTradeSpot : canTradeFutures;
    if (!hasPermission) {
      setError(`当前API权限不支持${marketType === 'spot' ? '现货' : '合约'}交易功能`);
      return;
    }
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const binanceService = new BinanceService(activeAccount);
      let result;

      if (marketType === 'spot') {
        result = await binanceService.createSpotOrder(
          formData.symbol,
          formData.type as 'market' | 'limit',
          formData.side,
          parseFloat(formData.amount),
          formData.type === 'limit' ? parseFloat(formData.price!) : undefined
        );
      } else {
        result = await binanceService.createFuturesOrder(
          formData.symbol,
          formData.type as 'market' | 'limit',
          formData.side,
          parseFloat(formData.amount),
          formData.type === 'limit' ? parseFloat(formData.price!) : undefined,
          parseFloat(formData.leverage!)
        );
      }

      if (result.success) {
        const orderInfo = result.data;
        setSuccess(`${formData.side === 'buy' ? '买入' : '卖出'}订单提交成功！订单ID: ${orderInfo?.id || 'N/A'}`);
        
        // 重置表单
        setFormData({
          symbol: selectedMarket.symbol,
          side: 'buy',
          type: 'market',
          amount: '',
          price: '',
          leverage: '1',
        });
        
        // 3秒后自动清除成功提示
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError(result.error || '订单提交失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '订单提交失败');
    } finally {
      setSubmitting(false);
    }
  };

  if (!activeAccount) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">交易</h2>
        <div className="text-center py-8">
          <p className="text-gray-500">请先选择一个活跃账户</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">交易</h2>

      {/* 市场类型选择 */}
      <div className="flex mb-6">
        <button
          onClick={() => handleMarketTypeChange('spot')}
          disabled={!canAccessSpot}
          className={`px-4 py-2 rounded-l-lg border ${
            marketType === 'spot'
              ? 'bg-blue-600 text-white border-blue-600'
              : !canAccessSpot
              ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`}
          title={!canAccessSpot ? '当前API权限不支持查看现货数据' : ''}
        >
          现货交易
          {!canAccessSpot && <span className="ml-1 text-red-500">🔒</span>}
        </button>
        <button
          onClick={() => handleMarketTypeChange('future')}
          disabled={!canAccessFutures}
          className={`px-4 py-2 rounded-r-lg border-t border-r border-b ${
            marketType === 'future'
              ? 'bg-blue-600 text-white border-blue-600'
              : !canAccessFutures
              ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`}
          title={!canAccessFutures ? '当前API权限不支持查看合约数据' : ''}
        >
          合约交易
          {!canAccessFutures && <span className="ml-1 text-red-500">🔒</span>}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 市场选择 */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">选择交易对</h3>
          
          <Input
            placeholder="搜索交易对..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="mb-4"
          />

          {loading ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
            </div>
          ) : (
            <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
              {filteredMarkets.map((market, index) => (
                <button
                  key={`${market.id}-${market.type}-${index}`}
                  onClick={() => handleMarketSelect(market)}
                  className={`w-full text-left px-4 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 ${
                    selectedMarket?.id === market.id ? 'bg-blue-50 text-blue-700' : ''
                  }`}
                >
                  <div className="font-medium">{market.symbol}</div>
                  <div className="text-sm text-gray-500">
                    {market.base}/{market.quote}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* 交易表单 */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {selectedMarket ? `交易 ${selectedMarket.symbol}` : '请选择交易对'}
          </h3>

          {selectedMarket && (
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* 买卖方向 */}
              <div className="flex">
                <button
                  type="button"
                  onClick={() => {
                    setFormData((prev: TradeForm) => ({ ...prev, side: 'buy' }));
                    setError(null);
                    setSuccess(null);
                  }}
                  className={`flex-1 py-2 rounded-l-lg border ${
                    formData.side === 'buy'
                      ? 'bg-green-600 text-white border-green-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  买入
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setFormData((prev: TradeForm) => ({ ...prev, side: 'sell' }));
                    setError(null);
                    setSuccess(null);
                  }}
                  className={`flex-1 py-2 rounded-r-lg border-t border-r border-b ${
                    formData.side === 'sell'
                      ? 'bg-red-600 text-white border-red-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  卖出
                </button>
              </div>

              {/* 订单类型 */}
              <div className="flex">
                <button
                  type="button"
                  onClick={() => {
                    setFormData((prev: TradeForm) => ({ ...prev, type: 'market' }));
                    setError(null);
                    setSuccess(null);
                  }}
                  className={`flex-1 py-2 rounded-l-lg border ${
                    formData.type === 'market'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  市价单
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setFormData((prev: TradeForm) => ({ 
                      ...prev, 
                      type: 'limit',
                      price: currentPrice || prev.price
                    }));
                    setError(null);
                    setSuccess(null);
                  }}
                  className={`flex-1 py-2 rounded-r-lg border-t border-r border-b ${
                    formData.type === 'limit'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  限价单
                </button>
              </div>

              {/* 杠杆设置（仅合约） */}
              {marketType === 'future' && (
                <Input
                  label="杠杆倍数"
                  type="number"
                  min="1"
                  max="125"
                  step="1"
                  value={formData.leverage}
                  onChange={(e) => {
                    setFormData((prev: TradeForm) => ({ ...prev, leverage: e.target.value }));
                    setError(null);
                    setSuccess(null);
                  }}
                  placeholder="输入杠杆倍数"
                />
              )}

              {/* 价格（限价单） */}
              {formData.type === 'limit' && (
                <div>
                  <Input
                    label="价格"
                    type="number"
                    step="any"
                    value={formData.price}
                    onChange={(e) => {
                      setFormData((prev: TradeForm) => ({ ...prev, price: e.target.value }));
                      setError(null);
                      setSuccess(null);
                    }}
                    placeholder="输入价格"
                  />
                  {currentPrice && (
                    <div className="mt-1 text-sm text-gray-500">
                      当前价格: {currentPrice} {selectedMarket.quote}
                    </div>
                  )}
                </div>
              )}

              {/* 数量 */}
              <Input
                label="数量"
                type="number"
                step="any"
                value={formData.amount}
                onChange={(e) => {
                  setFormData((prev: TradeForm) => ({ ...prev, amount: e.target.value }));
                  setError(null);
                  setSuccess(null);
                }}
                placeholder="输入数量"
              />

              {/* 总价值 */}
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-sm text-gray-600">总价值</div>
                <div className="font-medium">{calculateTotal()} {selectedMarket.quote}</div>
              </div>

              {!(marketType === 'spot' ? canTradeSpot : canTradeFutures) && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <p className="text-yellow-800 text-sm">当前API权限不支持{marketType === 'spot' ? '现货' : '合约'}交易功能，请检查API权限设置。</p>
                  </div>
                </div>
              )}

              {success && (
                <div className="bg-green-50 border border-green-200 rounded-md p-3">
                  <p className="text-green-800 text-sm">{success}</p>
                </div>
              )}

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3">
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              )}

              <Button
                type="submit"
                loading={submitting}
                disabled={!(marketType === 'spot' ? canTradeSpot : canTradeFutures)}
                className={`w-full ${
                  !(marketType === 'spot' ? canTradeSpot : canTradeFutures)
                    ? 'bg-gray-400 cursor-not-allowed'
                    : formData.side === 'buy'
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-red-600 hover:bg-red-700'
                }`}
                title={!(marketType === 'spot' ? canTradeSpot : canTradeFutures) ? `当前API权限不支持${marketType === 'spot' ? '现货' : '合约'}交易功能` : ''}
              >
                {!(marketType === 'spot' ? canTradeSpot : canTradeFutures) ? (
                  <span className="flex items-center">
                    🔒 权限不足
                  </span>
                ) : (
                  `${formData.side === 'buy' ? '买入' : '卖出'} ${selectedMarket.base}`
                )}
              </Button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}