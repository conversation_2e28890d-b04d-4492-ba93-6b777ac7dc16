'use client';

import React, { useState, useEffect } from 'react';
import { useAccounts } from '@/contexts/AccountContext';
import { Balance } from '@/types';
import { BinanceService } from '@/lib/binance';
import { Button } from '@/components/ui/Button';

export function BalanceView() {
  const { activeAccount } = useAccounts();
  const [balances, setBalances] = useState<Balance[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchBalances = async () => {
    if (!activeAccount) {
      setError('请先选择一个活跃账户');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const binanceService = new BinanceService(activeAccount);
      const result = await binanceService.getSpotBalance();

      if (result.success && result.data) {
        setBalances(result.data);
        setLastUpdated(new Date());
      } else {
        setError(result.error || '获取余额失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取余额失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeAccount) {
      fetchBalances();
    }
  }, [activeAccount]);

  const formatNumber = (value: string) => {
    const num = parseFloat(value);
    if (num === 0) return '0';
    if (num < 0.000001) return num.toExponential(2);
    if (num < 1) return num.toFixed(6);
    if (num < 1000) return num.toFixed(4);
    return num.toLocaleString();
  };

  const getTotalValue = () => {
    return balances.reduce((total, balance) => {
      const value = parseFloat(balance.usdValue || '0');
      return total + value;
    }, 0);
  };

  if (!activeAccount) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">现货余额</h2>
        <div className="text-center py-8">
          <p className="text-gray-500">请先选择一个活跃账户</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">现货余额</h2>
          <p className="text-sm text-gray-500">
            账户: {activeAccount.name}
            {activeAccount.isTestnet && ' (测试网)'}
          </p>
        </div>
        <Button onClick={fetchBalances} loading={loading}>
          刷新
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {lastUpdated && (
        <p className="text-sm text-gray-500 mb-4">
          最后更新: {lastUpdated.toLocaleString()}
        </p>
      )}

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">加载中...</p>
        </div>
      ) : balances.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">没有找到余额数据</p>
        </div>
      ) : (
        <>
          {/* 总价值显示 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="text-center">
              <p className="text-sm text-gray-600">总价值 (估算)</p>
              <p className="text-2xl font-bold text-gray-900">
                ${getTotalValue().toLocaleString()}
              </p>
            </div>
          </div>

          {/* 余额列表 */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    币种
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    可用余额
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    冻结余额
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    总余额
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    USD价值
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {balances.map((balance) => (
                  <tr key={balance.asset} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">
                          {balance.asset}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatNumber(balance.free)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatNumber(balance.locked)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatNumber(balance.total)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {balance.usdValue ? `$${formatNumber(balance.usdValue)}` : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
}