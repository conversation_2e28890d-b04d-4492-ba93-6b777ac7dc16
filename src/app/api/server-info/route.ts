import { NextResponse } from 'next/server';
import { networkInterfaces } from 'os';

export async function GET() {
  try {
    // 获取服务器网络接口信息
    const interfaces = networkInterfaces();
    let serverIP = '127.0.0.1';

    // 查找第一个非回环的IPv4地址
    for (const interfaceName in interfaces) {
      const interfaceInfo = interfaces[interfaceName];
      if (interfaceInfo) {
        for (const info of interfaceInfo) {
          if (info.family === 'IPv4' && !info.internal) {
            serverIP = info.address;
            break;
          }
        }
        if (serverIP !== '127.0.0.1') break;
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        ip: serverIP
      },
      message: '服务器IP获取成功'
    });
  } catch (error) {
    console.error('Get server IP error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取服务器IP失败',
      data: {
        ip: '127.0.0.1'
      }
    }, { status: 500 });
  }
}
