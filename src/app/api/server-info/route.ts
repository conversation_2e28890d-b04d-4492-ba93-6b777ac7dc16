import { NextRequest, NextResponse } from 'next/server';
import { networkInterfaces, hostname, platform, arch } from 'os';

export async function GET(request: NextRequest) {
  try {
    // 获取服务器网络接口信息
    const interfaces = networkInterfaces();
    let serverIP = '127.0.0.1';
    let localIPs: string[] = [];

    // 查找所有非回环的IPv4地址
    for (const interfaceName in interfaces) {
      const interfaceInfo = interfaces[interfaceName];
      if (interfaceInfo) {
        for (const info of interfaceInfo) {
          if (info.family === 'IPv4' && !info.internal) {
            localIPs.push(info.address);
            if (serverIP === '127.0.0.1') {
              serverIP = info.address;
            }
          }
        }
      }
    }

    // 获取请求头中的真实IP（如果通过代理）
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const clientIP = forwardedFor?.split(',')[0] || realIP || request.ip;

    return NextResponse.json({
      success: true,
      data: {
        ip: serverIP,
        localIPs,
        clientIP,
        hostname: hostname(),
        platform: platform(),
        arch: arch(),
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development'
      },
      message: '服务器信息获取成功'
    });
  } catch (error) {
    console.error('Get server info error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取服务器信息失败',
      data: {
        ip: '127.0.0.1',
        localIPs: [],
        clientIP: 'unknown',
        hostname: 'unknown',
        platform: 'unknown',
        arch: 'unknown',
        timestamp: new Date().toISOString(),
        environment: 'unknown'
      }
    }, { status: 500 });
  }
}
