import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // 直接请求 ipify API 获取公网IP
    const response = await fetch('https://api.ipify.org/?format=json');
    const data = await response.json();

    return NextResponse.json({
      success: true,
      data: {
        ip: data.ip
      },
      message: '服务器IP获取成功'
    });
  } catch (error) {
    console.error('Get server IP error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取服务器IP失败',
      data: {
        ip: '获取失败'
      }
    }, { status: 500 });
  }
}
