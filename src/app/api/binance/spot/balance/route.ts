import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';
import { Balance } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    // 创建币安交易所实例
    const exchange = new ccxt.binance({
      apiKey,
      secret: apiSecret,
      sandbox: isTestnet,
      options: {
        defaultType: 'spot',
      },
    });

    // 获取余额
    const balance = await exchange.fetchBalance();
    const balances: Balance[] = [];

    // 获取所有ticker价格信息
    let tickers: Record<string, { last?: number }> = {};
    try {
      tickers = await exchange.fetchTickers();
    } catch (error) {
      console.warn('Failed to fetch tickers for USD value calculation:', error);
    }

    // 直接从balance对象中获取币种信息
    for (const [asset, info] of Object.entries(balance)) {
      if (asset !== 'info' && asset !== 'free' && asset !== 'used' && asset !== 'total' && typeof info === 'object' && info !== null) {
        const balanceInfo = info as { free: number; used: number; total: number };
        if (balanceInfo.total > 0) {
          let usdValue = '0';
          
          // 计算USD价值
           if (asset === 'USDT' || asset === 'USDC' || asset === 'BUSD') {
             // 稳定币直接按1:1计算
             usdValue = balanceInfo.total.toFixed(2);
           } else {
             // 尝试通过不同的交易对获取USD价格
             const possiblePairs = [`${asset}/USDT`, `${asset}/USDC`, `${asset}/BUSD`];
             
             for (const pair of possiblePairs) {
               if (tickers[pair] && tickers[pair].last) {
                 const price = tickers[pair].last!;
                 usdValue = (balanceInfo.total * price).toFixed(2);
                 break;
               }
             }
           }
          
          balances.push({
            asset,
            free: balanceInfo.free.toString(),
            locked: balanceInfo.used.toString(),
            total: balanceInfo.total.toString(),
            usdValue,
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: balances
    });
  } catch (error) {
    console.error('Get balance error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取余额失败'
    }, { status: 500 });
  }
}