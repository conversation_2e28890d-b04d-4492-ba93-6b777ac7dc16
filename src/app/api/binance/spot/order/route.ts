import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';
import { Order } from '@/types';

interface CCXTOrderResult {
  id: string;
  symbol: string;
  side: string;
  type: string;
  amount: number;
  price?: number;
  status: string;
  filled: number;
  remaining: number;
  timestamp?: number;
  fee?: { cost?: number };
}

interface MarketInfo {
  symbol: string;
  base?: string;
  quote?: string;
  limits?: {
    amount?: {
      min?: number;
      max?: number;
    };
    cost?: {
      min?: number;
      max?: number;
    };
  };
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet, symbol, side, type, amount, price } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    if (!symbol || !side || !type || !amount) {
      return NextResponse.json({
        success: false,
        error: '交易参数不完整'
      }, { status: 400 });
    }

    if (type === 'limit' && !price) {
      return NextResponse.json({
        success: false,
        error: '限价单必须指定价格'
      }, { status: 400 });
    }

    // 创建币安交易所实例
    const exchange = new ccxt.binance({
      apiKey,
      secret: apiSecret,
      sandbox: isTestnet,
      options: {
        defaultType: 'spot',
      },
    });

    // 获取交易对信息进行验证
    let market: MarketInfo | undefined;
    try {
      const markets = await exchange.fetchMarkets();
      market = markets.find((m) => m?.symbol === symbol) as MarketInfo | undefined;
      if (!market) {
        return NextResponse.json({
          success: false,
          error: `交易对 ${symbol} 不存在或不可用`
        }, { status: 400 });
      }
    } catch (error) {
      console.warn('Failed to fetch market info:', error);
    }

    // 验证订单金额
    const orderAmount = parseFloat(amount);
    const orderPrice = price ? parseFloat(price) : null;

    if (market) {
      // 检查最小交易数量
      if (market.limits?.amount?.min && orderAmount < market.limits.amount.min) {
        return NextResponse.json({
          success: false,
          error: `交易数量不能小于 ${market.limits.amount.min} ${market.base || ''}`
        }, { status: 400 });
      }

      // 检查最大交易数量
      if (market.limits?.amount?.max && orderAmount > market.limits.amount.max) {
        return NextResponse.json({
          success: false,
          error: `交易数量不能大于 ${market.limits.amount.max} ${market.base || ''}`
        }, { status: 400 });
      }

      // 对于限价单，检查NOTIONAL（名义价值）
      if (type === 'limit' && orderPrice && market.limits?.cost?.min) {
        const notional = orderAmount * orderPrice;
        if (notional < market.limits.cost.min) {
          return NextResponse.json({
            success: false,
            error: `订单金额不能小于 ${market.limits.cost.min} ${market.quote || ''}（当前: ${notional.toFixed(2)}）`
          }, { status: 400 });
        }
      }

      // 对于市价单，获取当前价格估算NOTIONAL
      if (type === 'market' && market.limits?.cost?.min) {
        try {
          const ticker = await exchange.fetchTicker(symbol);
          const estimatedPrice = side === 'buy' ? ticker.ask : ticker.bid;
          if (estimatedPrice) {
            const estimatedNotional = orderAmount * estimatedPrice;
            if (estimatedNotional < market.limits.cost.min) {
              return NextResponse.json({
                success: false,
                error: `订单金额不能小于 ${market.limits.cost.min} ${market.quote || ''}（预估: ${estimatedNotional.toFixed(2)}）`
              }, { status: 400 });
            }
          }
        } catch (error) {
          console.warn('Failed to fetch ticker for notional validation:', error);
        }
      }
    }

    // 创建订单
    let order: CCXTOrderResult;
    if (type === 'market') {
      order = await exchange.createMarketOrder(symbol, side, orderAmount) as CCXTOrderResult;
    } else {
      order = await exchange.createLimitOrder(symbol, side, orderAmount, orderPrice!) as CCXTOrderResult;
    }
    
    const formattedOrder: Order = {
      id: order.id,
      symbol: order.symbol,
      side: order.side as 'buy' | 'sell',
      type: order.type as 'market' | 'limit' | 'stop' | 'stop_market',
      amount: order.amount.toString(),
      price: order.price?.toString(),
      status: order.status as 'open' | 'closed' | 'canceled' | 'partially_filled',
      filled: order.filled.toString(),
      remaining: order.remaining.toString(),
      timestamp: order.timestamp || Date.now(),
      fee: order.fee?.cost?.toString(),
    };

    return NextResponse.json({
      success: true,
      data: formattedOrder,
      message: '订单创建成功'
    });
  } catch (error) {
    console.error('Create order error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '创建订单失败'
    }, { status: 500 });
  }
}