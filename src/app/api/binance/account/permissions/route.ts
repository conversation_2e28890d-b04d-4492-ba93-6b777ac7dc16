import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';
import * as crypto from 'crypto';

interface ApiPermissions {
  ipRestrict: boolean;
  createTime: number;
  enableReading: boolean;
  enableWithdrawals: boolean;
  enableInternalTransfer: boolean;
  enableMargin: boolean;
  enableFutures: boolean;
  permitsUniversalTransfer: boolean;
  enableVanillaOptions: boolean;
  enableFixApiTrade: boolean;
  enableFixReadOnly: boolean;
  enableSpotAndMarginTrading: boolean;
  enablePortfolioMarginTrading: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    // 获取API权限信息
    // 注意：CCXT可能不直接支持这个端点，我们需要使用原生请求
    const timestamp = Date.now();
    const queryString = `timestamp=${timestamp}`;
    
    // 创建签名
    const signature = crypto
      .createHmac('sha256', apiSecret)
      .update(queryString)
      .digest('hex');

    const baseUrl = isTestnet 
      ? 'https://testnet.binance.vision'
      : 'https://api.binance.com';
    
    const url = `${baseUrl}/sapi/v1/account/apiRestrictions?${queryString}&signature=${signature}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-MBX-APIKEY': apiKey,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`API请求失败: ${response.status} ${errorData}`);
    }

    const permissions: ApiPermissions = await response.json();

    // 格式化权限信息为更友好的格式
    const formattedPermissions = {
      基本信息: {
        IP限制: permissions.ipRestrict ? '已启用' : '未启用',
        创建时间: new Date(permissions.createTime).toLocaleString('zh-CN'),
        读取权限: permissions.enableReading ? '已启用' : '未启用',
      },
      交易权限: {
        现货和杠杆交易: permissions.enableSpotAndMarginTrading ? '已启用' : '未启用',
        期货交易: permissions.enableFutures ? '已启用' : '未启用',
        杠杆交易: permissions.enableMargin ? '已启用' : '未启用',
        期权交易: permissions.enableVanillaOptions ? '已启用' : '未启用',
        组合保证金交易: permissions.enablePortfolioMarginTrading ? '已启用' : '未启用',
      },
      转账权限: {
        提现权限: permissions.enableWithdrawals ? '已启用' : '未启用',
        内部转账: permissions.enableInternalTransfer ? '已启用' : '未启用',
        通用转账: permissions.permitsUniversalTransfer ? '已启用' : '未启用',
      },
      API权限: {
        FIX_API交易: permissions.enableFixApiTrade ? '已启用' : '未启用',
        FIX_API只读: permissions.enableFixReadOnly ? '已启用' : '未启用',
      },
      原始数据: permissions
    };

    return NextResponse.json({
      success: true,
      data: formattedPermissions,
      message: 'API权限信息获取成功'
    });
  } catch (error) {
    console.error('Get API permissions error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取API权限信息失败'
    }, { status: 500 });
  }
}