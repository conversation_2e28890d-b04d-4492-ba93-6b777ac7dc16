import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';
import * as crypto from 'crypto';

interface LeverageBracket {
  symbol: string;
  brackets: Array<{
    bracket: number;
    initialLeverage: number;
    notionalCap: number;
    notionalFloor: number;
    maintMarginRatio: number;
    cum: number;
  }>;
}

interface PositionInfo {
  symbol: string;
  leverage: string;
  maxNotionalValue: string;
  marginType: string;
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    const baseUrl = isTestnet 
      ? 'https://testnet.binance.vision'
      : 'https://api.binance.com';

    const timestamp = Date.now();

    // 获取账户信息（包含杠杆设置）
    const accountQueryString = `timestamp=${timestamp}`;
    const accountSignature = crypto
      .createHmac('sha256', apiSecret)
      .update(accountQueryString)
      .digest('hex');

    const accountUrl = `${baseUrl}/fapi/v2/account?${accountQueryString}&signature=${accountSignature}`;
    
    const accountResponse = await fetch(accountUrl, {
      method: 'GET',
      headers: {
        'X-MBX-APIKEY': apiKey,
        'Content-Type': 'application/json',
      },
    });

    if (!accountResponse.ok) {
      const errorData = await accountResponse.text();
      throw new Error(`获取账户信息失败: ${accountResponse.status} ${errorData}`);
    }

    const accountData = await accountResponse.json();

    // 获取杠杆倍率信息
    const leverageQueryString = `timestamp=${timestamp}`;
    const leverageSignature = crypto
      .createHmac('sha256', apiSecret)
      .update(leverageQueryString)
      .digest('hex');

    const leverageUrl = `${baseUrl}/fapi/v1/leverageBracket?${leverageQueryString}&signature=${leverageSignature}`;
    
    const leverageResponse = await fetch(leverageUrl, {
      method: 'GET',
      headers: {
        'X-MBX-APIKEY': apiKey,
        'Content-Type': 'application/json',
      },
    });

    let leverageBrackets: LeverageBracket[] = [];
    if (leverageResponse.ok) {
      leverageBrackets = await leverageResponse.json();
    }

    // 获取持仓信息（包含当前杠杆设置）
    const positionQueryString = `timestamp=${timestamp}`;
    const positionSignature = crypto
      .createHmac('sha256', apiSecret)
      .update(positionQueryString)
      .digest('hex');

    const positionUrl = `${baseUrl}/fapi/v2/positionRisk?${positionQueryString}&signature=${positionSignature}`;
    
    const positionResponse = await fetch(positionUrl, {
      method: 'GET',
      headers: {
        'X-MBX-APIKEY': apiKey,
        'Content-Type': 'application/json',
      },
    });

    let positions: PositionInfo[] = [];
    if (positionResponse.ok) {
      positions = await positionResponse.json();
    }

    // 整理杠杆信息
    const leverageInfo = positions.map(pos => {
      const bracket = leverageBrackets.find(b => b.symbol === pos.symbol);
      const maxLeverage = bracket?.brackets[0]?.initialLeverage || 125;
      
      return {
        symbol: pos.symbol,
        leverage: parseInt(pos.leverage),
        maxLeverage: maxLeverage,
        marginType: pos.marginType.toLowerCase() as 'isolated' | 'cross'
      };
    });

    // 获取账户总体信息
    const accountInfo = {
      totalWalletBalance: accountData.totalWalletBalance || '0',
      totalUnrealizedProfit: accountData.totalUnrealizedProfit || '0',
      totalMarginBalance: accountData.totalMarginBalance || '0',
      totalPositionInitialMargin: accountData.totalPositionInitialMargin || '0',
      totalOpenOrderInitialMargin: accountData.totalOpenOrderInitialMargin || '0',
      maxWithdrawAmount: accountData.maxWithdrawAmount || '0',
      canTrade: accountData.canTrade || false,
      canDeposit: accountData.canDeposit || false,
      canWithdraw: accountData.canWithdraw || false,
    };

    return NextResponse.json({
      success: true,
      data: {
        accountInfo,
        leverageInfo,
        positionCount: positions.length,
        activePositions: positions.filter(p => parseFloat(p.leverage) > 0).length
      },
      message: '账户杠杆信息获取成功'
    });
  } catch (error) {
    console.error('Get account leverage error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取账户杠杆信息失败'
    }, { status: 500 });
  }
}
