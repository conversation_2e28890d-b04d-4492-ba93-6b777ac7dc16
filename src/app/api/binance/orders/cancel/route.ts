import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet, orderId, symbol } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    if (!orderId || !symbol) {
      return NextResponse.json({
        success: false,
        error: '订单ID和交易对不能为空'
      }, { status: 400 });
    }

    let cancelSuccess = false;
    let lastError: Error | null = null;

    // 尝试取消现货订单
    try {
      const spotExchange = new ccxt.binance({
        apiKey,
        secret: apiSecret,
        sandbox: isTestnet,
        options: {
          defaultType: 'spot',
        },
      });
      await spotExchange.cancelOrder(orderId, symbol);
      cancelSuccess = true;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('取消现货订单失败');
      console.warn('Failed to cancel spot order:', error);
    }

    // 如果现货订单取消失败，尝试取消合约订单
    if (!cancelSuccess) {
      try {
        const futuresExchange = new ccxt.binance({
          apiKey,
          secret: apiSecret,
          sandbox: isTestnet,
          options: {
            defaultType: 'future',
          },
        });
        await futuresExchange.cancelOrder(orderId, symbol);
        cancelSuccess = true;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('取消合约订单失败');
        console.warn('Failed to cancel futures order:', error);
      }
    }

    if (cancelSuccess) {
      return NextResponse.json({
        success: true,
        data: true,
        message: '订单取消成功'
      });
    } else {
      throw lastError || new Error('取消订单失败');
    }
  } catch (error) {
    console.error('Cancel order error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '取消订单失败'
    }, { status: 500 });
  }
}