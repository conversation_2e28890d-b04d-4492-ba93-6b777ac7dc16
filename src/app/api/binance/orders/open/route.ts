import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';
import { Order } from '@/types';

interface CCXTOrder {
  id: string;
  symbol: string;
  side: string;
  type: string;
  amount: number;
  price?: number;
  stopPrice?: number;
  status: string;
  filled: number;
  remaining: number;
  timestamp?: number;
  fee?: { cost?: number };
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet, symbols } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    // 默认查询的主要交易对
    const defaultSymbols = [
      'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'XRP/USDT',
      'SOL/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'MATIC/USDT'
    ];
    
    const symbolsToQuery = symbols && symbols.length > 0 ? symbols : defaultSymbols;

    let allOrders: CCXTOrder[] = [];

    // 获取现货订单
    try {
      const spotExchange = new ccxt.binance({
        apiKey,
        secret: apiSecret,
        sandbox: isTestnet,
        options: {
          defaultType: 'spot',
        },
      });
      
      for (const symbol of symbolsToQuery) {
        try {
          const spotOrders = await spotExchange.fetchOpenOrders(symbol);
          allOrders = allOrders.concat(spotOrders as CCXTOrder[]);
        } catch (error) {
          console.warn(`Failed to fetch spot orders for ${symbol}:`, error);
        }
      }
    } catch (error) {
      console.warn('Failed to initialize spot exchange:', error);
    }

    // 获取合约订单
    try {
      const futuresExchange = new ccxt.binance({
        apiKey,
        secret: apiSecret,
        sandbox: isTestnet,
        options: {
          defaultType: 'future',
        },
      });
      
      for (const symbol of symbolsToQuery) {
        try {
          const futuresOrders = await futuresExchange.fetchOpenOrders(symbol);
          allOrders = allOrders.concat(futuresOrders as CCXTOrder[]);
        } catch (error) {
          console.warn(`Failed to fetch futures orders for ${symbol}:`, error);
        }
      }
    } catch (error) {
      console.warn('Failed to initialize futures exchange:', error);
    }
    
    const formattedOrders: Order[] = allOrders.map((order) => ({
      id: order.id,
      symbol: order.symbol,
      side: order.side as 'buy' | 'sell',
      type: order.type as 'market' | 'limit' | 'stop' | 'stop_market',
      amount: order.amount.toString(),
      price: order.price?.toString(),
      stopPrice: order.stopPrice?.toString(),
      status: order.status as 'open' | 'closed' | 'canceled' | 'partially_filled',
      filled: order.filled.toString(),
      remaining: order.remaining.toString(),
      timestamp: order.timestamp || Date.now(),
      fee: order.fee?.cost?.toString(),
    }));

    return NextResponse.json({
      success: true,
      data: formattedOrders
    });
  } catch (error) {
    console.error('Get open orders error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取订单失败'
    }, { status: 500 });
  }
}