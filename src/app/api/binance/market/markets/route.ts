import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';
import { Market } from '@/types';

interface CCXTMarket {
  id: string;
  symbol: string;
  base: string;
  quote: string;
  active: boolean;
  type: string;
  precision: {
    amount: number;
    price: number;
  };
  limits: {
    amount: {
      min: number;
      max: number;
    };
    price: {
      min: number;
      max: number;
    };
  };
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    // 创建币安交易所实例
    const exchange = new ccxt.binance({
      apiKey,
      secret: apiSecret,
      sandbox: isTestnet,
      options: {
        defaultType: 'spot',
      },
    });

    // 获取市场信息
    const markets = await exchange.fetchMarkets();
    
    const formattedMarkets: Market[] = (markets as CCXTMarket[])
      .filter(market => market.active)
      .map((market, index) => ({
        id: `${market.id}-${market.type || 'spot'}-${index}`,
        symbol: market.symbol,
        base: market.base,
        quote: market.quote,
        active: market.active,
        type: market.type === 'future' ? 'future' : 'spot',
        precision: {
          amount: market.precision.amount,
          price: market.precision.price,
        },
        limits: {
          amount: {
            min: market.limits.amount.min,
            max: market.limits.amount.max,
          },
          price: {
            min: market.limits.price.min,
            max: market.limits.price.max,
          },
        },
      }));

    return NextResponse.json({
      success: true,
      data: formattedMarkets
    });
  } catch (error) {
    console.error('Get markets error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取市场信息失败'
    }, { status: 500 });
  }
}