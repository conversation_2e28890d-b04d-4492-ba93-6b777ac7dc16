import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';
import { MarketData } from '@/types';

interface CCXTTicker {
  symbol: string;
  last?: number;
  bid?: number;
  ask?: number;
  high?: number;
  low?: number;
  change?: number;
  percentage?: number;
  baseVolume?: number;
  quoteVolume?: number;
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet, symbol } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    if (!symbol) {
      return NextResponse.json({
        success: false,
        error: '交易对不能为空'
      }, { status: 400 });
    }

    // 创建币安交易所实例
    const exchange = new ccxt.binance({
      apiKey,
      secret: apiSecret,
      sandbox: isTestnet,
      options: {
        defaultType: 'spot',
      },
    });

    // 获取行情数据
    const ticker = await exchange.fetchTicker(symbol);
    
    const marketData: MarketData = {
      symbol: ticker.symbol,
      price: ticker.last?.toString() || '0',
      bid: ticker.bid?.toString() || '0',
      ask: ticker.ask?.toString() || '0',
      high24h: ticker.high?.toString() || '0',
      low24h: ticker.low?.toString() || '0',
      change24h: ticker.change?.toString() || '0',
      changePercent24h: ticker.percentage?.toString() || '0',
      volume24h: ticker.baseVolume?.toString() || '0',
      quoteVolume24h: ticker.quoteVolume?.toString() || '0',
    };

    return NextResponse.json({
      success: true,
      data: marketData
    });
  } catch (error) {
    console.error('Get ticker error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取行情失败'
    }, { status: 500 });
  }
}