import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    if (apiKey.length < 10 || apiSecret.length < 10) {
      return NextResponse.json({
        success: false,
        error: 'API密钥格式不正确'
      }, { status: 400 });
    }

    // 创建币安交易所实例
    const exchange = new ccxt.binance({
      apiKey,
      secret: apiSecret,
      sandbox: isTestnet,
      options: {
        defaultType: 'spot',
      },
    });

    // 测试API连接
    await exchange.fetchBalance();

    return NextResponse.json({
      success: true,
      data: true,
      message: 'API连接测试成功！'
    });
  } catch (error) {
    console.error('Test connection error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '连接测试失败'
    }, { status: 500 });
  }
}