import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';
import { Order } from '@/types';

interface CCXTOrderResult {
  id: string;
  symbol: string;
  side: string;
  type: string;
  amount: number;
  price?: number;
  status: string;
  filled: number;
  remaining: number;
  timestamp?: number;
  fee?: { cost?: number };
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet, symbol, side, type, amount, price, leverage } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    if (!symbol || !side || !type || !amount) {
      return NextResponse.json({
        success: false,
        error: '交易参数不完整'
      }, { status: 400 });
    }

    if (type === 'limit' && !price) {
      return NextResponse.json({
        success: false,
        error: '限价单必须指定价格'
      }, { status: 400 });
    }

    // 创建币安交易所实例（合约模式）
    const exchange = new ccxt.binance({
      apiKey,
      secret: apiSecret,
      sandbox: isTestnet,
      options: {
        defaultType: 'future',
      },
    });

    // 设置杠杆（如果提供）
    if (leverage) {
      await exchange.setLeverage(parseFloat(leverage), symbol);
    }

    // 创建订单
    let order: CCXTOrderResult;
    if (type === 'market') {
      order = await exchange.createMarketOrder(symbol, side, parseFloat(amount)) as CCXTOrderResult;
    } else {
      order = await exchange.createLimitOrder(symbol, side, parseFloat(amount), parseFloat(price)) as CCXTOrderResult;
    }
    
    const formattedOrder: Order = {
      id: order.id,
      symbol: order.symbol,
      side: order.side as 'buy' | 'sell',
      type: order.type as 'market' | 'limit' | 'stop' | 'stop_market',
      amount: order.amount.toString(),
      price: order.price?.toString(),
      status: order.status as 'open' | 'closed' | 'canceled' | 'partially_filled',
      filled: order.filled.toString(),
      remaining: order.remaining.toString(),
      timestamp: order.timestamp || Date.now(),
      fee: order.fee?.cost?.toString(),
    };

    return NextResponse.json({
      success: true,
      data: formattedOrder,
      message: '合约订单创建成功'
    });
  } catch (error) {
    console.error('Create futures order error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '创建合约订单失败'
    }, { status: 500 });
  }
}