import { NextRequest, NextResponse } from 'next/server';
import * as ccxt from 'ccxt';
import { Position } from '@/types';

interface CCXTPosition {
  symbol: string;
  side: string;
  contracts: number;
  entryPrice?: number;
  markPrice?: number;
  unrealizedPnl?: number;
  percentage?: number;
  leverage?: number;
  marginType?: string;
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey, apiSecret, isTestnet } = await request.json();

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        error: 'API密钥和密钥不能为空'
      }, { status: 400 });
    }

    // 创建币安交易所实例（合约模式）
    const exchange = new ccxt.binance({
      apiKey,
      secret: apiSecret,
      sandbox: isTestnet,
      options: {
        defaultType: 'future',
      },
    });

    // 获取持仓
    const positions = await exchange.fetchPositions();
    
    const activePositions: Position[] = (positions as CCXTPosition[])
      .filter((pos) => parseFloat(pos.contracts.toString()) > 0)
      .map((pos) => ({
        symbol: pos.symbol,
        side: pos.side as 'long' | 'short',
        size: pos.contracts.toString(),
        entryPrice: pos.entryPrice?.toString() || '0',
        markPrice: pos.markPrice?.toString() || '0',
        pnl: pos.unrealizedPnl?.toString() || '0',
        pnlPercentage: pos.percentage?.toString() || '0',
        leverage: pos.leverage?.toString() || '1',
        marginType: pos.marginType === 'isolated' ? 'isolated' : 'cross',
      }));

    return NextResponse.json({
      success: true,
      data: activePositions
    });
  } catch (error) {
    console.error('Get positions error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取持仓失败'
    }, { status: 500 });
  }
}