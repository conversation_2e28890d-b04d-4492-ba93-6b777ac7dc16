'use client';

import React, { useState, useEffect } from 'react';
import { AccountProvider } from '@/contexts/AccountContext';
import { PermissionsProvider, usePermissions } from '@/contexts/PermissionsContext';
import { AccountManager } from '@/components/AccountManager';
import { BalanceView } from '@/components/BalanceView';
import { PositionsView } from '@/components/PositionsView';
import { TradingForm } from '@/components/TradingForm';
import { ApiPermissionsView } from '@/components/ApiPermissionsView';
import { OrdersView } from '@/components/OrdersView';

type ViewType = 'spot' | 'futures' | 'orders' | 'permissions';

function HomePage() {
  const [activeView, setActiveView] = useState<ViewType>('spot');
  const [copied, setCopied] = useState(false);
  const [serverInfo, setServerInfo] = useState<string>('获取中...');
  const { canAccessSpot, canAccessFutures, loading: permissionsLoading, canTradeSpot, canTradeFutures } = usePermissions();

  useEffect(() => {
    // 获取服务器IP地址
    const getServerIP = async () => {
      try {
        const response = await fetch('/api/server-info');
        const data = await response.json();
        if (data.success) {
          setServerInfo(data.data.ip);
        } else {
          setServerInfo('获取失败');
        }
      } catch (error) {
        console.error('获取服务器IP失败:', error);
        setServerInfo('获取失败');
      }
    };

    getServerIP();
  }, []);

   const handleCopyIP = async () => {
     try {
       await navigator.clipboard.writeText(serverInfo);
       setCopied(true);
       setTimeout(() => setCopied(false), 2000);
     } catch (err) {
       console.error('复制失败:', err);
     }
   };

  return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-900">币安交易管理</h1>
                <span className="ml-2 text-sm text-gray-500">BAM</span>
                <div className="ml-4 flex items-center">
                  <button
                    onClick={handleCopyIP}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors cursor-pointer"
                    title="点击复制IP地址"
                  >
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-1.5"></span>
                    {serverInfo}
                    {copied && (
                      <span className="ml-1.5 text-green-600">✓</span>
                    )}
                  </button>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-500">
                  专业交易者的一站式管理平台
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Main Layout */}
        <div className="flex h-[calc(100vh-64px)]">
          {/* Left Sidebar - Account List */}
          <div className="w-80 bg-white shadow-lg border-r border-gray-200 overflow-y-auto">
            <div className="p-8">
              <div className="mb-8">
                <h2 className="text-xl font-bold text-gray-900 mb-2">账户管理</h2>
                <p className="text-sm text-gray-500">管理您的币安交易账户</p>
              </div>
              <AccountManager />
            </div>
          </div>

          {/* Right Content Area */}
          <div className="flex-1 overflow-y-auto bg-gray-50">
            <div className="p-8">
              {/* View Toggle */}
              <div className="mb-8">
                <div className="flex space-x-1 bg-white p-1 rounded-xl shadow-sm border border-gray-200 w-fit">
                  <button
                    onClick={() => setActiveView('spot')}
                    disabled={!canAccessSpot && !permissionsLoading}
                    className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeView === 'spot'
                        ? 'bg-blue-600 text-white shadow-md'
                        : !canAccessSpot && !permissionsLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                    }`}
                    title={!canAccessSpot && !permissionsLoading ? '当前API权限不支持现货交易' : ''}
                  >
                    现货交易
                    {!canAccessSpot && !permissionsLoading && (
                      <span className="ml-1 text-red-500">🔒</span>
                    )}
                  </button>
                  <button
                    onClick={() => setActiveView('futures')}
                    disabled={!canAccessFutures && !permissionsLoading}
                    className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeView === 'futures'
                        ? 'bg-blue-600 text-white shadow-md'
                        : !canAccessFutures && !permissionsLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                    }`}
                    title={!canAccessFutures && !permissionsLoading ? '当前API权限不支持合约交易' : ''}
                  >
                    合约交易
                    {!canAccessFutures && !permissionsLoading && (
                      <span className="ml-1 text-red-500">🔒</span>
                    )}
                  </button>
                  <button
                    onClick={() => setActiveView('orders')}
                    disabled={(!canAccessSpot && !canAccessFutures) && !permissionsLoading}
                    className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeView === 'orders'
                        ? 'bg-blue-600 text-white shadow-md'
                        : (!canAccessSpot && !canAccessFutures) && !permissionsLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                    }`}
                    title={(!canAccessSpot && !canAccessFutures) && !permissionsLoading ? '当前API权限不支持查看订单' : ''}
                  >
                    订单管理
                    {(!canAccessSpot && !canAccessFutures) && !permissionsLoading && (
                      <span className="ml-1 text-red-500">🔒</span>
                    )}
                  </button>
                  <button
                    onClick={() => setActiveView('permissions')}
                    className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeView === 'permissions'
                        ? 'bg-blue-600 text-white shadow-md'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                    }`}
                  >
                    API权限
                  </button>
                </div>
              </div>

              {/* Content Area */}
              {activeView === 'spot' && (
                <div className="space-y-8">
                  {!canAccessSpot && !permissionsLoading ? (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                      <div className="flex items-center justify-center mb-4">
                        <svg className="w-12 h-12 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-yellow-800 mb-2">权限不足</h3>
                      <p className="text-yellow-700 mb-4">当前API密钥没有读取权限，无法查看现货数据，请检查API权限设置或联系管理员。</p>
                      <button
                        onClick={() => setActiveView('permissions')}
                        className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors"
                      >
                        查看API权限
                      </button>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                      <BalanceView />
                      <TradingForm />
                    </div>
                  )}
                </div>
              )}

              {activeView === 'futures' && (
                <div className="space-y-8">
                  {!canAccessFutures && !permissionsLoading ? (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                      <div className="flex items-center justify-center mb-4">
                        <svg className="w-12 h-12 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-yellow-800 mb-2">权限不足</h3>
                      <p className="text-yellow-700 mb-4">当前API密钥没有读取权限，无法查看合约数据，请检查API权限设置或联系管理员。</p>
                      <button
                        onClick={() => setActiveView('permissions')}
                        className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors"
                      >
                        查看API权限
                      </button>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                      <PositionsView />
                      <TradingForm />
                    </div>
                  )}
                </div>
              )}

              {activeView === 'orders' && (
                <div className="space-y-8">
                  {(!canAccessSpot && !canAccessFutures) && !permissionsLoading ? (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                      <div className="flex items-center justify-center mb-4">
                        <svg className="w-12 h-12 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-yellow-800 mb-2">权限不足</h3>
                      <p className="text-yellow-700 mb-4">当前API密钥没有读取权限，无法查看订单数据，请检查API权限设置或联系管理员。</p>
                      <button
                        onClick={() => setActiveView('permissions')}
                        className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors"
                      >
                        查看API权限
                      </button>
                    </div>
                  ) : (
                    <OrdersView />
                  )}
                </div>
              )}

              {activeView === 'permissions' && (
                <div className="space-y-8">
                  <ApiPermissionsView />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-white border-t border-gray-200 mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center text-gray-500 text-sm">
              <p>币安交易管理应用 - 安全、高效、专业</p>
              <p className="mt-2">⚠️ 请确保API密钥安全，建议使用测试网进行测试</p>
            </div>
          </div>
        </footer>
      </div>
  );
}

export default function Home() {
  return (
    <AccountProvider>
      <PermissionsProvider>
        <HomePage />
      </PermissionsProvider>
    </AccountProvider>
  );
}
