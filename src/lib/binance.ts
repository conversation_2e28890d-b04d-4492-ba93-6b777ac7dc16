import { Account, Balance, Position, Order, Ticker, Market, MarketData, ApiResponse, ApiPermissions, LeverageInfo } from '@/types';

export class BinanceService {
  private apiKey: string;
  private apiSecret: string;
  private isTestnet: boolean;

  constructor(account: Account) {
    this.apiKey = account.apiKey;
    this.apiSecret = account.apiSecret;
    this.isTestnet = account.isTestnet;
  }

  private async apiCall<T>(endpoint: string, data?: Record<string, unknown>): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          apiKey: this.apiKey,
          apiSecret: this.apiSecret,
          isTestnet: this.isTestnet,
          ...data,
        }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '网络请求失败'
      };
    }
  }

  // 测试API连接
  async testConnection(): Promise<ApiResponse<boolean>> {
    return this.apiCall<boolean>('/api/binance/test-connection');
  }

  // 获取现货余额
  async getSpotBalance(): Promise<ApiResponse<Balance[]>> {
    return this.apiCall<Balance[]>('/api/binance/spot/balance');
  }

  // 获取合约持仓
  async getFuturesPositions(): Promise<ApiResponse<Position[]>> {
    return this.apiCall<Position[]>('/api/binance/futures/positions');
  }

  // 获取未成交订单
  async getOpenOrders(symbols?: string[]): Promise<ApiResponse<Order[]>> {
    return this.apiCall<Order[]>('/api/binance/orders/open', { symbols });
  }

  // 获取市场行情
  async getTicker(symbol: string): Promise<ApiResponse<MarketData>> {
    return this.apiCall<MarketData>('/api/binance/market/ticker', { symbol });
  }

  // 获取所有市场
  async getMarkets(): Promise<ApiResponse<Market[]>> {
    return this.apiCall<Market[]>('/api/binance/market/markets');
  }

  // 创建现货订单
  async createSpotOrder(
    symbol: string,
    type: 'market' | 'limit',
    side: 'buy' | 'sell',
    amount: number,
    price?: number
  ): Promise<ApiResponse<Order>> {
    return this.apiCall<Order>('/api/binance/spot/order', {
      symbol,
      type,
      side,
      amount,
      price
    });
  }

  // 创建合约订单
  async createFuturesOrder(
    symbol: string,
    type: 'market' | 'limit',
    side: 'buy' | 'sell',
    amount: number,
    price?: number,
    leverage?: number
  ): Promise<ApiResponse<Order>> {
    return this.apiCall<Order>('/api/binance/futures/order', {
      symbol,
      type,
      side,
      amount,
      price,
      leverage
    });
  }

  // 取消订单
  async cancelOrder(orderId: string, symbol: string): Promise<ApiResponse<boolean>> {
    return this.apiCall<boolean>('/api/binance/orders/cancel', {
      orderId,
      symbol
    });
  }

  // 获取API权限信息
  async getApiPermissions(): Promise<ApiResponse<ApiPermissions>> {
    return this.apiCall<ApiPermissions>('/api/binance/account/permissions');
  }

  // 获取账户杠杆信息
  async getAccountLeverage(): Promise<ApiResponse<{
    accountInfo: any;
    leverageInfo: LeverageInfo[];
    positionCount: number;
    activePositions: number;
  }>> {
    return this.apiCall('/api/binance/account/leverage');
  }
}