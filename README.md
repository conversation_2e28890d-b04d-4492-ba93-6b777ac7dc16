# 币安交易管理应用 (BAM)

一个基于 Next.js 和 CCXT 构建的专业币安交易管理平台，为交易者提供一站式的账户管理、资产监控和交易执行功能。

## 🚀 核心功能

### 🏦 账户管理
- **多账户支持**：管理多个币安API账户（主网/测试网）
- **账户配置**：添加、编辑、删除交易账户
- **API密钥管理**：安全存储和管理币安API凭证
- **连接测试**：验证API密钥有效性

### 💰 资产查看
- **现货余额查询**：实时获取现货账户所有币种余额
- **合约持仓监控**：查看期货合约持仓、盈亏、杠杆等信息
- **资产总览**：统计账户总价值和收益情况

### 📊 交易功能
- **现货交易**：支持市价单、限价单等多种订单类型
- **合约交易**：支持期货合约开仓、平仓操作
- **订单管理**：查看和取消未成交挂单
- **实时行情**：获取交易对价格信息

### 🔍 实时监控
- **挂单跟踪**：实时监控所有待成交订单状态
- **持仓监控**：实时跟踪合约持仓盈亏变化
- **多市场支持**：同时管理现货和合约市场

### 🛡️ 安全特性
- **测试网支持**：支持币安测试网进行安全测试
- **本地存储**：API密钥仅存储在浏览器本地
- **权限验证**：完整的API权限和网络验证

## 🛠️ 技术栈

- **前端框架**：Next.js 15.4.4
- **UI库**：Tailwind CSS 4.0
- **交易所API**：CCXT 4.2.25
- **状态管理**：React Context
- **类型检查**：TypeScript
- **图标**：Heroicons

## 📦 安装和运行

### 环境要求
- Node.js 18.0 或更高版本
- npm 或 yarn

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd bam
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run dev
```

4. 打开浏览器访问 `http://localhost:3000`

## 🔧 使用指南

### 1. 添加账户
1. 点击「账户管理」标签
2. 点击「添加账户」按钮
3. 填写账户信息：
   - 账户名称：自定义名称
   - API Key：币安API密钥
   - API Secret：币安API密钥密码
   - 测试网：是否使用测试网
4. 点击「测试连接」验证API密钥
5. 点击「添加」保存账户

### 2. 查看资产
1. 确保已选择活跃账户
2. 在「总览」页面查看：
   - 现货余额：显示所有币种余额
   - 合约持仓：显示期货持仓信息
   - 未成交订单：显示待成交订单

### 3. 执行交易
1. 点击「交易」标签
2. 选择交易类型（现货/合约）
3. 搜索并选择交易对
4. 设置交易参数：
   - 买入/卖出方向
   - 订单类型（市价/限价）
   - 数量和价格
   - 杠杆倍数（合约交易）
5. 点击提交订单

## ✅ 功能完整可用

**好消息！** 所有功能现在都可以正常使用：

- ✅ **真实 API 连接**：通过 Next.js API Routes 实现，无 CORS 限制
- ✅ **完整交易功能**：支持现货和合约交易
- ✅ **实时数据**：余额、持仓、订单、行情等实时更新
- ✅ **安全可靠**：API 密钥在服务端处理，不暴露给客户端

### 技术架构

应用采用 Next.js 全栈架构：
- **前端**：React + TypeScript + Tailwind CSS
- **后端**：Next.js API Routes + CCXT
- **数据流**：前端组件 → API Routes → 币安 API

## ⚠️ 安全提醒

1. **API密钥安全**：
   - 仅授予必要的权限（现货交易、期货交易）
   - 不要授予提现权限
   - 定期更换API密钥

2. **测试建议**：
   - 首次使用建议在测试网环境测试
   - 小额资金测试功能正常性
   - 确认理解所有功能后再进行大额交易

3. **数据安全**：
   - API密钥仅存储在浏览器本地
   - 不会上传到任何服务器
   - 清除浏览器数据会删除所有账户信息

## 🔗 币安API设置

### 获取API密钥
1. 登录币安账户
2. 进入「API管理」页面
3. 创建新的API密钥
4. 设置权限：
   - ✅ 现货交易
   - ✅ 期货交易
   - ❌ 提现（不建议开启）
5. 记录API Key和Secret

### 测试网设置
- 测试网地址：https://testnet.binance.vision/
- 注册测试网账户
- 获取测试网API密钥
- 在应用中勾选「使用测试网」

## 📝 开发说明

### 项目结构
```
src/
├── app/                 # Next.js App Router
├── components/          # React 组件
│   ├── ui/             # 通用UI组件
│   ├── AccountManager.tsx
│   ├── BalanceView.tsx
│   ├── PositionsView.tsx
│   ├── OrdersView.tsx
│   └── TradingForm.tsx
├── contexts/           # React Context
├── lib/               # 工具库
│   └── binance.ts     # 币安API封装
└── types/             # TypeScript类型定义
```

### 主要组件
- `AccountManager`：账户管理组件
- `BalanceView`：余额显示组件
- `PositionsView`：持仓监控组件
- `OrdersView`：订单管理组件
- `TradingForm`：交易表单组件
- `BinanceService`：币安API服务类

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

MIT License

## ⚠️ 免责声明

本应用仅供学习和研究使用。使用本应用进行实际交易的风险由用户自行承担。开发者不对任何交易损失负责。
